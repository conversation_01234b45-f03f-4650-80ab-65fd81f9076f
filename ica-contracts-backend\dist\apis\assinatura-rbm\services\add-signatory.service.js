"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AddSignatoryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddSignatoryService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("axios");
const FormData = require("form-data");
const auth_service_1 = require("./auth.service");
let AddSignatoryService = AddSignatoryService_1 = class AddSignatoryService {
    constructor(configService, authIntegrationService) {
        this.configService = configService;
        this.authIntegrationService = authIntegrationService;
        this.logger = new common_1.Logger(AddSignatoryService_1.name);
        this.apiUrl = `${this.configService.get('API_ASSINATURA_URL')}/documentos`;
        this.axiosInstance = axios_1.default.create({
            baseURL: this.apiUrl,
        });
    }
    async addSignatory(data) {
        const authResponse = await this.authIntegrationService.authenticateIntegration();
        const { token } = authResponse.jwt;
        const formData = new FormData();
        formData.append('cpfCnpj', data.cpfCnpj);
        formData.append('nome', data.nome);
        formData.append('tipoAss', data.tipoAss);
        formData.append('tipoAut', 'email');
        if (data.tipoAssComplemento)
            formData.append('tipoAssComplemento', data.tipoAssComplemento);
        if (data.grupoAssinaturaId)
            formData.append('grupoAssinaturaId', data.grupoAssinaturaId);
        if (data.ordemAssinatura)
            formData.append('ordemAssinatura', data.ordemAssinatura);
        if (data.email)
            formData.append('email', data.email);
        if (data.celular)
            formData.append('celular', data.celular);
        if (data.linkAssinaturaEmail)
            formData.append('linkAssinaturaEmail', data.linkAssinaturaEmail);
        if (data.linkAssinaturaSMS)
            formData.append('linkAssinaturaSMS', data.linkAssinaturaSMS);
        if (data.dataNasc)
            formData.append('dataNasc', data.dataNasc);
        if (data.selfieNecessaria)
            formData.append('selfieNecessaria', data.selfieNecessaria);
        if (data.certificadoDigitalNecessario)
            formData.append('certificadoDigitalNecessario', data.certificadoDigitalNecessario);
        if (data.documentoFrenteNecessario)
            formData.append('documentoFrenteNecessario', data.documentoFrenteNecessario);
        if (data.documentoVersoNecessario)
            formData.append('documentoVersoNecessario', data.documentoVersoNecessario);
        if (data.selfieComDocumentoNecessario)
            formData.append('selfieComDocumentoNecessario', data.selfieComDocumentoNecessario);
        if (data.serproRealizarValidacaoFacial)
            formData.append('serproRealizarValidacaoFacial', data.serproRealizarValidacaoFacial);
        if (data.faceMatch)
            formData.append('faceMatch', data.faceMatch);
        if (data.mensagem)
            formData.append('mensagem', data.mensagem);
        try {
            const response = await this.axiosInstance.post(`/addSig/${data.id}`, formData, {
                headers: {
                    ...formData.getHeaders(),
                    Authorization: `Bearer ${token}`,
                },
            });
            if (response.data.erro) {
                this.logger.error(`Erro ao adicionar signatário: ${response.data.message}`);
                throw new common_1.InternalServerErrorException(response.data.message);
            }
            return response.data;
        }
        catch (error) {
            this.logger.error('Erro ao adicionar signatário', error);
            if (error.response) {
                throw new common_1.InternalServerErrorException(`Erro da API: ${error.response.status} - ${error.response.data.message || error.response.data}`);
            }
            else if (error.request) {
                throw new common_1.InternalServerErrorException('Nenhuma resposta recebida da API.');
            }
            else {
                throw new common_1.InternalServerErrorException(`Erro na requisição: ${error.message}`);
            }
        }
    }
};
exports.AddSignatoryService = AddSignatoryService;
exports.AddSignatoryService = AddSignatoryService = AddSignatoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        auth_service_1.AuthIntegrationService])
], AddSignatoryService);
//# sourceMappingURL=add-signatory.service.js.map