import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { Repository, In } from 'typeorm';

import { AbstractContractHandler } from '../abstract-contract.handler';
import { ContractContext } from '../contract.context';
import { AdvisorAssignmentDto } from 'src/modules/contract/dto/create-contract-manual.dto';

@Injectable()
export class ValidateAdvisorHandler extends AbstractContractHandler<
  ContractContext,
  void
> {
  private readonly logger = new Logger(ValidateAdvisorHandler.name);

  constructor(
    @InjectRepository(WalletsViewsEntity)
    private readonly walletsViewsRepository: Repository<WalletsViewsEntity>,
  ) {
    super();
  }

  async handle(context: ContractContext): Promise<void> {
    if (context.userProfile.role.name === RolesEnum.BROKER) {
      context.brokerId = context.userProfile.id;

      const advisorsList = context.dto.advisors ?? [];

      const allAdvisorsAreValid = await this.areAllAdvisorsValid(
        context.userProfile.id,
        advisorsList,
      );

      if (!allAdvisorsAreValid) {
        this.logger.error(
          `Broker ${context.userProfile.id} tentou validar advisors, mas nem todos estão vinculados corretamente.
           Advisors recebidos: ${JSON.stringify(advisorsList)}`,
        );

        throw new BadRequestException(
          'Alguns advisors não são válidos para este broker.',
        );
      }

      context.advisors = advisorsList;
      this.logger.log(
        `Broker ${context.userProfile.id} validou com sucesso os advisors: ${JSON.stringify(advisorsList)}`,
      );
    }

    if (context.userProfile.role.name === RolesEnum.ADMIN) {
      // Admin atua como broker, mas precisa encontrar o broker associado
      // Por enquanto, vamos usar o próprio admin como brokerId
      context.brokerId = context.userProfile.id;
      context.advisors = context.dto.advisors ?? [];

      this.logger.log(
        `Admin ${context.userProfile.id} criando contrato. Advisors: ${JSON.stringify(context.advisors)}`,
      );
    }

    if (context.userProfile.role.name === RolesEnum.ADVISOR) {
      // Buscar o broker associado ao advisor através da tabela wallets-views
      const walletRelation = await this.walletsViewsRepository.findOne({
        where: {
          bottomId: context.userProfile.id,
          bottom: {
            role: { name: RolesEnum.ADVISOR },
          },
          upper: {
            role: { name: RolesEnum.BROKER },
          },
        },
        relations: ['upper', 'upper.role', 'bottom', 'bottom.role'],
      });

      if (!walletRelation) {
        this.logger.error(
          `Advisor ${context.userProfile.id} não possui um broker associado. Bottom relations: ${JSON.stringify(context.userProfile.bottom)}`,
        );
        throw new BadRequestException('Broker não encontrado para o advisor.');
      }

      context.brokerId = walletRelation.upperId;

      if (!context.userProfile.partPercent) {
        this.logger.error(
          `Advisor ${context.userProfile.id} não possui uma porcentagem de participação definida. PartPercent: ${context.userProfile.partPercent}`,
        );
        throw new BadRequestException('Porcentagem de participação não encontrada.');
      }

      context.advisors = [
        {
          advisorId: context.userProfile.id,
          rate: Number(context.userProfile.partPercent),
        },
      ];

      this.logger.log(
        `Advisor ${context.userProfile.id} definido no contexto como único advisor. Broker associado: ${context.brokerId}`,
      );
    }

    await super.handle(context);
  }

  private async areAllAdvisorsValid(
    brokerId: string,
    advisors: AdvisorAssignmentDto[] = [],
  ): Promise<boolean> {
    const advisorsCount = advisors.length;
    const advisorsIds = advisors.map((advisor) => advisor.advisorId);

    if (advisorsCount === 0) {
      return true;
    }

    const validAdvisorsCount = await this.walletsViewsRepository.count({
      where: {
        upperId: brokerId,
        bottom: {
          id: In(advisorsIds),
          role: { name: RolesEnum.ADVISOR },
        },
      },
    });

    return validAdvisorsCount === advisorsCount;
  }
}
