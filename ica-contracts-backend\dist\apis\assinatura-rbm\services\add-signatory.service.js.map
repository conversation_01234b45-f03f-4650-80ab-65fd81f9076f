{"version": 3, "file": "add-signatory.service.js", "sourceRoot": "/", "sources": ["apis/assinatura-rbm/services/add-signatory.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAIwB;AACxB,2CAA+C;AAC/C,iCAA6C;AAC7C,sCAAsC;AAGtC,iDAAwD;AAGjD,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAK9B,YACmB,aAA4B,EAC5B,sBAA8C;QAD9C,kBAAa,GAAb,aAAa,CAAe;QAC5B,2BAAsB,GAAtB,sBAAsB,CAAwB;QAJhD,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;QAM7D,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oBAAoB,CAAC,aAAa,CAAC;QAEnF,IAAI,CAAC,aAAa,GAAG,eAAK,CAAC,MAAM,CAAC;YAChC,OAAO,EAAE,IAAI,CAAC,MAAM;SACrB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,IAAiC;QAClD,MAAM,YAAY,GAChB,MAAM,IAAI,CAAC,sBAAsB,CAAC,uBAAuB,EAAE,CAAC;QAC9D,MAAM,EAAE,KAAK,EAAE,GAAG,YAAY,CAAC,GAAG,CAAC;QAEnC,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAChC,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEpC,IAAI,IAAI,CAAC,kBAAkB;YACzB,QAAQ,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjE,IAAI,IAAI,CAAC,iBAAiB;YACxB,QAAQ,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/D,IAAI,IAAI,CAAC,eAAe;YACtB,QAAQ,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3D,IAAI,IAAI,CAAC,KAAK;YAAE,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,IAAI,CAAC,OAAO;YAAE,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,IAAI,CAAC,mBAAmB;YAC1B,QAAQ,CAAC,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACnE,IAAI,IAAI,CAAC,iBAAiB;YACxB,QAAQ,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/D,IAAI,IAAI,CAAC,QAAQ;YAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9D,IAAI,IAAI,CAAC,gBAAgB;YACvB,QAAQ,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC7D,IAAI,IAAI,CAAC,4BAA4B;YACnC,QAAQ,CAAC,MAAM,CACb,8BAA8B,EAC9B,IAAI,CAAC,4BAA4B,CAClC,CAAC;QACJ,IAAI,IAAI,CAAC,yBAAyB;YAChC,QAAQ,CAAC,MAAM,CACb,2BAA2B,EAC3B,IAAI,CAAC,yBAAyB,CAC/B,CAAC;QACJ,IAAI,IAAI,CAAC,wBAAwB;YAC/B,QAAQ,CAAC,MAAM,CACb,0BAA0B,EAC1B,IAAI,CAAC,wBAAwB,CAC9B,CAAC;QACJ,IAAI,IAAI,CAAC,4BAA4B;YACnC,QAAQ,CAAC,MAAM,CACb,8BAA8B,EAC9B,IAAI,CAAC,4BAA4B,CAClC,CAAC;QACJ,IAAI,IAAI,CAAC,6BAA6B;YACpC,QAAQ,CAAC,MAAM,CACb,+BAA+B,EAC/B,IAAI,CAAC,6BAA6B,CACnC,CAAC;QACJ,IAAI,IAAI,CAAC,SAAS;YAAE,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACjE,IAAI,IAAI,CAAC,QAAQ;YAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE9D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAC5C,WAAW,IAAI,CAAC,EAAE,EAAE,EACpB,QAAQ,EACR;gBACE,OAAO,EAAE;oBACP,GAAG,QAAQ,CAAC,UAAU,EAAE;oBACxB,aAAa,EAAE,UAAU,KAAK,EAAE;iBACjC;aACF,CACF,CAAC;YAEF,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CACzD,CAAC;gBACF,MAAM,IAAI,qCAA4B,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChE,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YAEzD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,MAAM,IAAI,qCAA4B,CACpC,gBAAgB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAChG,CAAC;YACJ,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBACzB,MAAM,IAAI,qCAA4B,CACpC,mCAAmC,CACpC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,qCAA4B,CACpC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CACvC,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AA5GY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAOuB,sBAAa;QACJ,qCAAsB;GAPtD,mBAAmB,CA4G/B", "sourcesContent": ["import {\r\n  Injectable,\r\n  InternalServerErrorException,\r\n  Logger,\r\n} from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport axios, { AxiosInstance } from 'axios';\r\nimport * as FormData from 'form-data';\r\n\r\nimport { IAdicionarSignatarioRequest } from '../requests/add-signatory.request';\r\nimport { AuthIntegrationService } from './auth.service';\r\n\r\n@Injectable()\r\nexport class AddSignatoryService {\r\n  private readonly axiosInstance: AxiosInstance;\r\n  private readonly apiUrl: string;\r\n  private readonly logger = new Logger(AddSignatoryService.name);\r\n\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly authIntegrationService: AuthIntegrationService,\r\n  ) {\r\n    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/documentos`;\r\n\r\n    this.axiosInstance = axios.create({\r\n      baseURL: this.apiUrl,\r\n    });\r\n  }\r\n\r\n  async addSignatory(data: IAdicionarSignatarioRequest) {\r\n    const authResponse =\r\n      await this.authIntegrationService.authenticateIntegration();\r\n    const { token } = authResponse.jwt;\r\n\r\n    const formData = new FormData();\r\n    formData.append('cpfCnpj', data.cpfCnpj);\r\n    formData.append('nome', data.nome);\r\n    formData.append('tipoAss', data.tipoAss);\r\n    formData.append('tipoAut', 'email');\r\n\r\n    if (data.tipoAssComplemento)\r\n      formData.append('tipoAssComplemento', data.tipoAssComplemento);\r\n    if (data.grupoAssinaturaId)\r\n      formData.append('grupoAssinaturaId', data.grupoAssinaturaId);\r\n    if (data.ordemAssinatura)\r\n      formData.append('ordemAssinatura', data.ordemAssinatura);\r\n    if (data.email) formData.append('email', data.email);\r\n    if (data.celular) formData.append('celular', data.celular);\r\n    if (data.linkAssinaturaEmail)\r\n      formData.append('linkAssinaturaEmail', data.linkAssinaturaEmail);\r\n    if (data.linkAssinaturaSMS)\r\n      formData.append('linkAssinaturaSMS', data.linkAssinaturaSMS);\r\n    if (data.dataNasc) formData.append('dataNasc', data.dataNasc);\r\n    if (data.selfieNecessaria)\r\n      formData.append('selfieNecessaria', data.selfieNecessaria);\r\n    if (data.certificadoDigitalNecessario)\r\n      formData.append(\r\n        'certificadoDigitalNecessario',\r\n        data.certificadoDigitalNecessario,\r\n      );\r\n    if (data.documentoFrenteNecessario)\r\n      formData.append(\r\n        'documentoFrenteNecessario',\r\n        data.documentoFrenteNecessario,\r\n      );\r\n    if (data.documentoVersoNecessario)\r\n      formData.append(\r\n        'documentoVersoNecessario',\r\n        data.documentoVersoNecessario,\r\n      );\r\n    if (data.selfieComDocumentoNecessario)\r\n      formData.append(\r\n        'selfieComDocumentoNecessario',\r\n        data.selfieComDocumentoNecessario,\r\n      );\r\n    if (data.serproRealizarValidacaoFacial)\r\n      formData.append(\r\n        'serproRealizarValidacaoFacial',\r\n        data.serproRealizarValidacaoFacial,\r\n      );\r\n    if (data.faceMatch) formData.append('faceMatch', data.faceMatch);\r\n    if (data.mensagem) formData.append('mensagem', data.mensagem);\r\n\r\n    try {\r\n      const response = await this.axiosInstance.post(\r\n        `/addSig/${data.id}`,\r\n        formData,\r\n        {\r\n          headers: {\r\n            ...formData.getHeaders(),\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        },\r\n      );\r\n\r\n      if (response.data.erro) {\r\n        this.logger.error(\r\n          `Erro ao adicionar signatário: ${response.data.message}`,\r\n        );\r\n        throw new InternalServerErrorException(response.data.message);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      this.logger.error('Erro ao adicionar signatário', error);\r\n\r\n      if (error.response) {\r\n        throw new InternalServerErrorException(\r\n          `Erro da API: ${error.response.status} - ${error.response.data.message || error.response.data}`,\r\n        );\r\n      } else if (error.request) {\r\n        throw new InternalServerErrorException(\r\n          'Nenhuma resposta recebida da API.',\r\n        );\r\n      } else {\r\n        throw new InternalServerErrorException(\r\n          `Erro na requisição: ${error.message}`,\r\n        );\r\n      }\r\n    }\r\n  }\r\n}\r\n"]}