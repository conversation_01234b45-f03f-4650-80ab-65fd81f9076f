{"version": 3, "file": "add-many-signatories.request.js", "sourceRoot": "/", "sources": ["apis/assinatura-rbm/requests/add-many-signatories.request.ts"], "names": [], "mappings": "", "sourcesContent": ["interface ISignatory {\r\n  nome: string;\r\n  cpfCnpj: string;\r\n  email?: string;\r\n  celular?: string;\r\n  dataNasc?: string;\r\n  tipoAssinatura: string;\r\n  tipoAssinaturaComplemento?: string;\r\n  grupoAssinaturaId?: number;\r\n  ordemAssinatura?: number;\r\n  tipoAutenticacao: string;\r\n  selfieNecessaria?: string;\r\n  documentoFrenteNecessario?: string;\r\n  documentoVersoNecessario?: string;\r\n  selfieComDocumentoNecessario?: string;\r\n  certificadoDigitalNecessario?: string;\r\n  serproRealizarValidacaoFacial?: string;\r\n  faceMatch?: string;\r\n}\r\n\r\nexport interface IAddSignatoriesRequest {\r\n  mensagem: string;\r\n  signatarios: ISignatory[];\r\n}\r\n"]}