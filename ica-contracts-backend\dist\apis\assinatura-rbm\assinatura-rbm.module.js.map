{"version": 3, "file": "assinatura-rbm.module.js", "sourceRoot": "/", "sources": ["apis/assinatura-rbm/assinatura-rbm.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AAExC,0FAAoF;AACpF,4EAAuE;AACvE,0DAAiE;AACjE,gFAA2E;AAC3E,sFAAgF;AAChF,4EAAuE;AACvE,4EAAuE;AACvE,oFAA+E;AAyBxE,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;CAAG,CAAA;AAAtB,kDAAmB;8BAAnB,mBAAmB;IAvB/B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,EAAE;QACX,SAAS,EAAE;YACT,qCAAsB;YACtB,2CAAmB;YACnB,2CAAmB;YACnB,wDAAyB;YACzB,oDAAuB;YACvB,2CAAmB;YACnB,+CAAqB;YACrB,mDAAuB;SACxB;QACD,OAAO,EAAE;YACP,qCAAsB;YACtB,2CAAmB;YACnB,2CAAmB;YACnB,wDAAyB;YACzB,oDAAuB;YACvB,2CAAmB;YACnB,+CAAqB;YACrB,mDAAuB;SACxB;KACF,CAAC;GACW,mBAAmB,CAAG", "sourcesContent": ["import { Modu<PERSON> } from '@nestjs/common';\r\n\r\nimport { AddManySignatoriesService } from './services/add-many-signatories.service';\r\nimport { AddSignatoryService } from './services/add-signatory.service';\r\nimport { AuthIntegrationService } from './services/auth.service';\r\nimport { FindSignatarieService } from './services/find-signatarie.service';\r\nimport { ListAllDocumentsService } from './services/list-all-documents.service';\r\nimport { DocumentInfoService } from './services/list-document.service';\r\nimport { SendDocumentService } from './services/send-document.service';\r\nimport { SendNotificationService } from './services/send-notification.service';\r\n\r\n@Module({\r\n  imports: [],\r\n  providers: [\r\n    AuthIntegrationService,\r\n    SendDocumentService,\r\n    AddSignatoryService,\r\n    AddManySignatoriesService,\r\n    ListAllDocumentsService,\r\n    DocumentInfoService,\r\n    FindSignatarieService,\r\n    SendNotificationService,\r\n  ],\r\n  exports: [\r\n    AuthIntegrationService,\r\n    SendDocumentService,\r\n    AddSignatoryService,\r\n    AddManySignatoriesService,\r\n    ListAllDocumentsService,\r\n    DocumentInfoService,\r\n    FindSignatarieService,\r\n    SendNotificationService,\r\n  ],\r\n})\r\nexport class AssinaturaRbmModule {}\r\n"]}