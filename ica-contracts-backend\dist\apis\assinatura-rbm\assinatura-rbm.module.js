"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssinaturaRbmModule = void 0;
const common_1 = require("@nestjs/common");
const add_many_signatories_service_1 = require("./services/add-many-signatories.service");
const add_signatory_service_1 = require("./services/add-signatory.service");
const auth_service_1 = require("./services/auth.service");
const find_signatarie_service_1 = require("./services/find-signatarie.service");
const list_all_documents_service_1 = require("./services/list-all-documents.service");
const list_document_service_1 = require("./services/list-document.service");
const send_document_service_1 = require("./services/send-document.service");
const send_notification_service_1 = require("./services/send-notification.service");
let AssinaturaRbmModule = class AssinaturaRbmModule {
};
exports.AssinaturaRbmModule = AssinaturaRbmModule;
exports.AssinaturaRbmModule = AssinaturaRbmModule = __decorate([
    (0, common_1.Module)({
        imports: [],
        providers: [
            auth_service_1.AuthIntegrationService,
            send_document_service_1.SendDocumentService,
            add_signatory_service_1.AddSignatoryService,
            add_many_signatories_service_1.AddManySignatoriesService,
            list_all_documents_service_1.ListAllDocumentsService,
            list_document_service_1.DocumentInfoService,
            find_signatarie_service_1.FindSignatarieService,
            send_notification_service_1.SendNotificationService,
        ],
        exports: [
            auth_service_1.AuthIntegrationService,
            send_document_service_1.SendDocumentService,
            add_signatory_service_1.AddSignatoryService,
            add_many_signatories_service_1.AddManySignatoriesService,
            list_all_documents_service_1.ListAllDocumentsService,
            list_document_service_1.DocumentInfoService,
            find_signatarie_service_1.FindSignatarieService,
            send_notification_service_1.SendNotificationService,
        ],
    })
], AssinaturaRbmModule);
//# sourceMappingURL=assinatura-rbm.module.js.map