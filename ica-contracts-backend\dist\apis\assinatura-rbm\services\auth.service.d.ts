import { ConfigService } from '@nestjs/config';
import { IAuthResponse } from '../responses/auth.response';
export declare class AuthIntegrationService {
    private readonly configService;
    private readonly axiosInstance;
    private readonly apiUrl;
    private readonly authDto;
    private readonly logger;
    constructor(configService: ConfigService);
    authenticateIntegration(): Promise<IAuthResponse>;
}
