{"version": 3, "file": "add-many-signatories.service.js", "sourceRoot": "/", "sources": ["apis/assinatura-rbm/services/add-many-signatories.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAIwB;AACxB,2CAA+C;AAC/C,iCAA6C;AAI7C,iDAAwD;AAGjD,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IAKpC,YACmB,aAA4B,EAC5B,sBAA8C;QAD9C,kBAAa,GAAb,aAAa,CAAe;QAC5B,2BAAsB,GAAtB,sBAAsB,CAAwB;QAJhD,WAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;QAMnE,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oBAAoB,CAAC,aAAa,CAAC;QAEnF,IAAI,CAAC,aAAa,GAAG,eAAK,CAAC,MAAM,CAAC;YAChC,OAAO,EAAE,IAAI,CAAC,MAAM;SACrB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,EAAU,EACV,IAA4B;QAE5B,MAAM,YAAY,GAChB,MAAM,IAAI,CAAC,sBAAsB,CAAC,uBAAuB,EAAE,CAAC;QAC9D,MAAM,EAAE,KAAK,EAAE,GAAG,YAAY,CAAC,GAAG,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAC5C,gBAAgB,EAAE,EAAE,EACpB,IAAI,EACJ;gBACE,OAAO,EAAE;oBACP,aAAa,EAAE,UAAU,KAAK,EAAE;oBAChC,cAAc,EAAE,kBAAkB;iBACnC;aACF,CACF,CAAC;YAEF,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAErD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,MAAM,IAAI,qCAA4B,CACpC,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAC9E,CAAC;YACJ,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBACzB,MAAM,IAAI,qCAA4B,CACpC,gCAAgC,CACjC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,qCAA4B,CACpC,kBAAkB,KAAK,CAAC,OAAO,EAAE,CAClC,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAvDY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;qCAOuB,sBAAa;QACJ,qCAAsB;GAPtD,yBAAyB,CAuDrC", "sourcesContent": ["import {\r\n  Injectable,\r\n  InternalServerErrorException,\r\n  Logger,\r\n} from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport axios, { AxiosInstance } from 'axios';\r\n\r\nimport { IAddSignatoriesRequest } from '../requests/add-many-signatories.request';\r\nimport { IAddSignatoriesResponse } from '../responses/add-many-signatories.response';\r\nimport { AuthIntegrationService } from './auth.service';\r\n\r\n@Injectable()\r\nexport class AddManySignatoriesService {\r\n  private readonly axiosInstance: AxiosInstance;\r\n  private readonly apiUrl: string;\r\n  private readonly logger = new Logger(AddManySignatoriesService.name);\r\n\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly authIntegrationService: AuthIntegrationService,\r\n  ) {\r\n    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/documentos`;\r\n\r\n    this.axiosInstance = axios.create({\r\n      baseURL: this.apiUrl,\r\n    });\r\n  }\r\n\r\n  async addSignatories(\r\n    id: string,\r\n    data: IAddSignatoriesRequest,\r\n  ): Promise<IAddSignatoriesResponse> {\r\n    const authResponse =\r\n      await this.authIntegrationService.authenticateIntegration();\r\n    const { token } = authResponse.jwt;\r\n\r\n    try {\r\n      const response = await this.axiosInstance.post<IAddSignatoriesResponse>(\r\n        `/signatarios/${id}`,\r\n        data,\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n          },\r\n        },\r\n      );\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      this.logger.error('Error adding signatories', error);\r\n\r\n      if (error.response) {\r\n        throw new InternalServerErrorException(\r\n          `${error.response.status} - ${error.response.data.payload.status[0].message}`,\r\n        );\r\n      } else if (error.request) {\r\n        throw new InternalServerErrorException(\r\n          'No response received from API.',\r\n        );\r\n      } else {\r\n        throw new InternalServerErrorException(\r\n          `Request error: ${error.message}`,\r\n        );\r\n      }\r\n    }\r\n  }\r\n}\r\n"]}