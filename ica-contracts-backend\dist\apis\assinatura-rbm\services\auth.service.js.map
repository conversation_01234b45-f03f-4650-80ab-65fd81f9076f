{"version": 3, "file": "auth.service.js", "sourceRoot": "/", "sources": ["apis/assinatura-rbm/services/auth.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAIwB;AACxB,2CAA+C;AAC/C,iCAA6C;AAI7C,MAAM,kBAAkB;CAKvB;AAGM,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAMjC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAFxC,WAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;QAGhE,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oBAAoB,CAAC,UAAU,CAAC;QAChF,IAAI,CAAC,OAAO,GAAG;YACb,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kBAAkB,CAAC;YACzD,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kBAAkB,CAAC;YACzD,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kBAAkB,CAAC;YACzD,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CACpC,4BAA4B,CAC7B;SACF,CAAC;QAEF,IAAI,CAAC,aAAa,GAAG,eAAK,CAAC,MAAM,CAAC;YAChC,OAAO,EAAE,IAAI,CAAC,MAAM;SACrB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,uBAAuB;QAC3B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAC5C,EAAE,EACF,IAAI,CAAC,OAAO,CACb,CAAC;YAEF,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAEnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAE1D,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,MAAM,IAAI,qCAA4B,CACpC,gBAAgB,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAChG,CAAC;YACJ,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBACzB,MAAM,IAAI,qCAA4B,CACpC,mCAAmC,CACpC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,qCAA4B,CACpC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CACvC,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAlDY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;qCAOiC,sBAAa;GAN9C,sBAAsB,CAkDlC", "sourcesContent": ["import {\r\n  Injectable,\r\n  Logger,\r\n  InternalServerErrorException,\r\n} from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport axios, { AxiosInstance } from 'axios';\r\n\r\nimport { IAuthResponse } from '../responses/auth.response';\r\n\r\nclass AuthIntegrationDto {\r\n  email?: string;\r\n  senha: string;\r\n  login?: string;\r\n  hashIntegracao: string;\r\n}\r\n\r\n@Injectable()\r\nexport class AuthIntegrationService {\r\n  private readonly axiosInstance: AxiosInstance;\r\n  private readonly apiUrl: string;\r\n  private readonly authDto: AuthIntegrationDto;\r\n  private readonly logger = new Logger(AuthIntegrationService.name);\r\n\r\n  constructor(private readonly configService: ConfigService) {\r\n    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/v2/auth`;\r\n    this.authDto = {\r\n      email: this.configService.get<string>('ASSINATURA_EMAIL'),\r\n      senha: this.configService.get<string>('ASSINATURA_SENHA'),\r\n      login: this.configService.get<string>('ASSINATURA_LOGIN'),\r\n      hashIntegracao: this.configService.get<string>(\r\n        'ASSINATURA_HASH_INTEGRACAO',\r\n      ),\r\n    };\r\n\r\n    this.axiosInstance = axios.create({\r\n      baseURL: this.apiUrl,\r\n    });\r\n  }\r\n\r\n  async authenticateIntegration(): Promise<IAuthResponse> {\r\n    try {\r\n      const response = await this.axiosInstance.post<IAuthResponse>(\r\n        '',\r\n        this.authDto,\r\n      );\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.log(error);\r\n\r\n      this.logger.error('Erro ao autenticar integração', error);\r\n\r\n      if (error.response) {\r\n        throw new InternalServerErrorException(\r\n          `Erro da API: ${error.response.status} - ${error.response.data.message || error.response.data}`,\r\n        );\r\n      } else if (error.request) {\r\n        throw new InternalServerErrorException(\r\n          'Nenhuma resposta recebida da API.',\r\n        );\r\n      } else {\r\n        throw new InternalServerErrorException(\r\n          `Erro na requisição: ${error.message}`,\r\n        );\r\n      }\r\n    }\r\n  }\r\n}\r\n"]}