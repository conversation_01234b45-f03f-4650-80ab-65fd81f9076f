{"version": 3, "file": "auth.response.js", "sourceRoot": "/", "sources": ["apis/assinatura-rbm/responses/auth.response.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IJwtResponse {\r\n  type: string;\r\n  token: string;\r\n  refreshToken: string;\r\n}\r\n\r\nexport interface IAuthResponse {\r\n  erro: boolean;\r\n  jwt?: IJwtResponse;\r\n  user?: unknown;\r\n  message?: string;\r\n}\r\n"]}