{"version": 3, "file": "add-signatory.request.js", "sourceRoot": "/", "sources": ["apis/assinatura-rbm/requests/add-signatory.request.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IAdicionarSignatarioRequest {\r\n  id: string;\r\n\r\n  cpfCnpj: string;\r\n  nome: string;\r\n  tipoAss: string;\r\n\r\n  tipoAssComplemento?: string;\r\n  grupoAssinaturaId?: string;\r\n  ordemAssinatura?: string;\r\n  email: string;\r\n  celular?: string;\r\n  linkAssinaturaEmail?: string;\r\n  linkAssinaturaSMS?: string;\r\n  dataNasc?: string;\r\n  selfieNecessaria?: string;\r\n  certificadoDigitalNecessario?: string;\r\n  documentoFrenteNecessario?: string;\r\n  documentoVersoNecessario?: string;\r\n  selfieComDocumentoNecessario?: string;\r\n  serproRealizarValidacaoFacial?: string;\r\n  faceMatch?: string;\r\n  mensagem?: string;\r\n}\r\n"]}