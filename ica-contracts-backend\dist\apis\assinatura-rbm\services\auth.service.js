"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthIntegrationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthIntegrationService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("axios");
class AuthIntegrationDto {
}
let AuthIntegrationService = AuthIntegrationService_1 = class AuthIntegrationService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(AuthIntegrationService_1.name);
        this.apiUrl = `${this.configService.get('API_ASSINATURA_URL')}/v2/auth`;
        this.authDto = {
            email: this.configService.get('ASSINATURA_EMAIL'),
            senha: this.configService.get('ASSINATURA_SENHA'),
            login: this.configService.get('ASSINATURA_LOGIN'),
            hashIntegracao: this.configService.get('ASSINATURA_HASH_INTEGRACAO'),
        };
        this.axiosInstance = axios_1.default.create({
            baseURL: this.apiUrl,
        });
    }
    async authenticateIntegration() {
        try {
            const response = await this.axiosInstance.post('', this.authDto);
            return response.data;
        }
        catch (error) {
            console.log(error);
            this.logger.error('Erro ao autenticar integração', error);
            if (error.response) {
                throw new common_1.InternalServerErrorException(`Erro da API: ${error.response.status} - ${error.response.data.message || error.response.data}`);
            }
            else if (error.request) {
                throw new common_1.InternalServerErrorException('Nenhuma resposta recebida da API.');
            }
            else {
                throw new common_1.InternalServerErrorException(`Erro na requisição: ${error.message}`);
            }
        }
    }
};
exports.AuthIntegrationService = AuthIntegrationService;
exports.AuthIntegrationService = AuthIntegrationService = AuthIntegrationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], AuthIntegrationService);
//# sourceMappingURL=auth.service.js.map