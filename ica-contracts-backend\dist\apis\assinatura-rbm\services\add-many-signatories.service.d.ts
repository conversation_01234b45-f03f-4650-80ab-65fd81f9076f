import { ConfigService } from '@nestjs/config';
import { IAddSignatoriesRequest } from '../requests/add-many-signatories.request';
import { IAddSignatoriesResponse } from '../responses/add-many-signatories.response';
import { AuthIntegrationService } from './auth.service';
export declare class AddManySignatoriesService {
    private readonly configService;
    private readonly authIntegrationService;
    private readonly axiosInstance;
    private readonly apiUrl;
    private readonly logger;
    constructor(configService: ConfigService, authIntegrationService: AuthIntegrationService);
    addSignatories(id: string, data: IAddSignatoriesRequest): Promise<IAddSignatoriesResponse>;
}
