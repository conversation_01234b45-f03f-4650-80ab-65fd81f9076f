"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AddManySignatoriesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddManySignatoriesService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("axios");
const auth_service_1 = require("./auth.service");
let AddManySignatoriesService = AddManySignatoriesService_1 = class AddManySignatoriesService {
    constructor(configService, authIntegrationService) {
        this.configService = configService;
        this.authIntegrationService = authIntegrationService;
        this.logger = new common_1.Logger(AddManySignatoriesService_1.name);
        this.apiUrl = `${this.configService.get('API_ASSINATURA_URL')}/documentos`;
        this.axiosInstance = axios_1.default.create({
            baseURL: this.apiUrl,
        });
    }
    async addSignatories(id, data) {
        const authResponse = await this.authIntegrationService.authenticateIntegration();
        const { token } = authResponse.jwt;
        try {
            const response = await this.axiosInstance.post(`/signatarios/${id}`, data, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
            return response.data;
        }
        catch (error) {
            this.logger.error('Error adding signatories', error);
            if (error.response) {
                throw new common_1.InternalServerErrorException(`${error.response.status} - ${error.response.data.payload.status[0].message}`);
            }
            else if (error.request) {
                throw new common_1.InternalServerErrorException('No response received from API.');
            }
            else {
                throw new common_1.InternalServerErrorException(`Request error: ${error.message}`);
            }
        }
    }
};
exports.AddManySignatoriesService = AddManySignatoriesService;
exports.AddManySignatoriesService = AddManySignatoriesService = AddManySignatoriesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        auth_service_1.AuthIntegrationService])
], AddManySignatoriesService);
//# sourceMappingURL=add-many-signatories.service.js.map